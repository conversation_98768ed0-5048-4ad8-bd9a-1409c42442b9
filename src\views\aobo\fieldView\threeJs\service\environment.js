import * as THREE from 'three'
import { Color, LoadingManager } from 'three'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader'
import store from '@/store'
import { camera, orbitControls, scene } from '@/views/aobo/fieldView/threeJs/service/core'
import linePoints from '@/assets/center-line/linePoints.json'
import A01 from '@/assets/center-line/A01.json'
import A02 from '@/assets/center-line/path_points.json'
import B01 from '@/assets/center-line/B01.json'
import B02 from '@/assets/center-line/B02.json'
import C01 from '@/assets/center-line/C01.json'
import C02 from '@/assets/center-line/C02.json'
import gsap from 'gsap'

/**
 * 根据温度生成颜色的函数 (温度范围: 30-70°C)
 * @param {number} temperature - 温度值
 * @returns {string} - 返回十六进制颜色值
 */
function getTemperatureColor(temperature) {
  // 定义起始和结束颜色
  const startColor = { r: 0x24, g: 0x7e, b: 0xe6 } // #247ee6
  const endColor = { r: 0xff, g: 0x00, b: 0x59 } // #ff0059

  // 限制温度范围在 30-70 之间
  const normalizedTemp = Math.max(30, Math.min(70, temperature))

  // 将温度转换为 0-1 的比例 (30度对应0，70度对应1)
  const ratio = (normalizedTemp - 30) / (70 - 30)

  // 线性插值计算各颜色通道的值
  const r = Math.round(startColor.r + (endColor.r - startColor.r) * ratio)
  const g = Math.round(startColor.g + (endColor.g - startColor.g) * ratio)
  const b = Math.round(startColor.b + (endColor.b - startColor.b) * ratio)

  // 转换为十六进制字符串
  const hex = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`

  return hex
}
const gui = null
/**
 * 中轴线数据
 * */
const centerLineList = [
  // { name: '管廊', points: linePoints, color: 'blue' },
  // { name: 'A01', points: A01, color: 'red' },
  // { name: 'A02', points: A02, color: 'green' },
  // { name: 'B01', points: B01, color: 'cyan' },
  // { name: 'B02', points: B02, color: 'magenta' },
  // { name: 'C01', points: C01, color: 'yellow' },
  { name: 'C02', points: C02, color: 'orange' },
]

export class Environment {
  loadingManager

  rgbeLoader

  // GLTF模型对象
  model

  singleCabinetModel

  // 加载进度
  progress = 0

  // 模型尺寸
  modelSize

  // 存储一些常用资源
  assets = {
    mainBuilding: null,
  }

  textureObj = {}

  constructor() {
    this.rgbeLoader = new RGBELoader(this.loadingManager)
    this.#initLoadingManager()
    this.#addLight()
    this.#loadModel()
    this.#loadTexture()
    setTimeout(() => {
      this.#loadCenterLine()
    }, 5000)

    // scene.add(new THREE.AxesHelper(100000))
  }

  /** 初始化加载管理器*/
  #initLoadingManager() {
    const loadingManager = new LoadingManager()
    this.loadingManager = loadingManager
    loadingManager.onLoad = function() {
      setTimeout(() => {
        store.dispatch('threeJs/setLoaded', true)
      }, 500)
    }

    loadingManager.onProgress = (item, loaded, total) => {
      this.progress = (loaded / total) * 100
      store.dispatch('threeJs/setProgress', this.progress)
    }

    loadingManager.onError = function(url) {
      console.log(`Error loading ${url}`)
    }
  }

  /** 加载纹理贴图*/
  #loadTexture() {
    const loader = new THREE.TextureLoader(this.loadingManager)
    loader.load('texture/deng_002.jpg', (texture) => {
      console.log(texture, 'texture/deng_002.jpg')
      texture.colorSpace = THREE.SRGBColorSpace
      // 移除等距柱状投影映射，使用默认的UV映射
      // texture.mapping = THREE.EquirectangularReflectionMapping

      // 设置纹理沿着管道长度方向重复 ClampToEdgeWrapping
      texture.wrapS = THREE.RepeatWrapping // U方向（沿管道长度）
      texture.wrapT = THREE.ClampToEdgeWrapping // V方向（围绕管道）

      // 调整重复次数，让纹理沿着管道长度方向平铺
      // 可以根据需要调整这个值来控制纹理的密度
      texture.repeat.set(23000, 0) // U方向重复5次，V方向不重复

      this.textureObj.tube = texture
    })
  }

  /** 加载环境纹理*/
  #loadEnvironmentTexture() {
    const hdrTexture = this.rgbeLoader.load('model/env.hdr', (texture) => {
      texture.colorSpace = THREE.SRGBColorSpace
      texture.mapping = THREE.EquirectangularReflectionMapping
      scene.environment = texture
      scene.background = texture
    })
  }

  /** 加载模型*/
  #loadModel() {
    const dracoLoader = new DRACOLoader()
    dracoLoader.setDecoderPath('draco/')
    const loader = new GLTFLoader(this.loadingManager)
    loader.setDRACOLoader(dracoLoader)
    loader.load('model/temp.glb', (gltf) => {
      const modelScene = gltf.scene
      scene.add(modelScene)

      this.singleCabinetModel = modelScene

      const changeMaterial = (mesh) => {
        mesh.castShadow = false
        mesh.receiveShadow = false
        // 销毁之前的材质，节约性能
        mesh.material.dispose()
        mesh.material = null
        mesh.material = new THREE.MeshLambertMaterial({
          transparent: true,
          // color: new THREE.Color(color),
          color: new THREE.Color('#1cbfd8'),
          // color: isDoor ? new THREE.Color('#ff6666') : new THREE.Color('#4C6B9A'),
          opacity: 0.3,
          side: THREE.DoubleSide,
        })
      }

      // 不需要更换材质的模型
      const noChangeMaterialList = ['电线']
      // 需要更换材质的模型
      const nameList = ['管廊', '地形', '房屋', '线箱', '支架', '吊装']
      modelScene.traverse((child) => {
        if (child.name.includes('管道')) {
          child.visible = false
        }
        if (child.name.includes('管廊')) {
          child.visible = false
        }

        if (child instanceof THREE.Mesh) {
          // 将材质金属度降低，以便其能接受环境光
          child.material.metalness = 0.3
          child.material.specularIntensity = 1
        }
      })

      modelScene.children.forEach((child) => {
        for (const name of noChangeMaterialList) {
          if (child.name.includes(name)) {
            return
          }
        }

        let flag
        for (const name of nameList) {
          if (child.name.includes(name)) {
            flag = true
            break
          }
        }

        if (flag) {
          if (child instanceof THREE.Group) {
            for (const el of child.children) {
              if (el instanceof THREE.Mesh) {
                changeMaterial(el)
              }
            }
          } else if (child instanceof THREE.Mesh) {
            changeMaterial(child)
          }
        }
      })
      // modelScene.updateMatrixWorld(true)
      dracoLoader.dispose()

      // 将视角定位到光缆起点
      const position = new THREE.Vector3(-539, -32, -9)
      orbitControls.target.copy(position)
      orbitControls.update()
      gsap.to(camera.position, {
        x: -614,
        y: -3,
        z: -38,
        duration: 1,
        ease: 'power2.inOut',
      })
    })
  }

  /** 加载中轴线*/
  #loadCenterLine() {
    for (const [index, item] of centerLineList.entries()) {
      // 清空现有的points数组
      const points = []

      // 添加真实数据
      for (const point of item.points) {
        points.push(new THREE.Vector3(point.x, point.y, point.z))
      }

      // 创建曲线
      const curve = new THREE.CatmullRomCurve3(points)
      // 优化：减少曲线点数量以提升性能
      const finalPoints = curve.getPoints(1000)

      // 创建管道中轴线
      const geometry = new THREE.BufferGeometry().setFromPoints(finalPoints)
      const material = new THREE.LineBasicMaterial({ color: item.color }) // 青色
      const line = new THREE.Line(geometry, material)
      line.name = item.name
      // line.rotation.x = -Math.PI / 2
      line.visible = false
      scene.add(line)
      const totalLength = curve.getLength()

      // 绘制管道
      const tubeGeometry = new THREE.TubeGeometry(curve, 1000, 0.1, 14, false)
      const tubeMaterial = new THREE.MeshBasicMaterial({ map: this.textureObj.tube, side: THREE.DoubleSide })
      const tube = new THREE.Mesh(tubeGeometry, tubeMaterial)
      tube.name = item.name
      // tube.visible = false
      scene.add(tube)
      // 分段列表
      const list = [
        {
          start: 0,
          end: 50,
          id: 1,
          temperature: 60,
        },
        {
          start: 50,
          end: 150,
          id: 2,
          temperature: 38,
        },
        {
          start: 150,
          end: 250,
          id: 3,
          temperature: 48,
        },
        {
          start: 250,
          end: 350,
          id: 4,
          temperature: 28,
        },
        {
          start: 350,
          end: 450,
          id: 5,
          temperature: 58,
        },
        {
          start: 450,
          end: 500,
          id: 6,
          temperature: 38,
        },
        {
          start: 500,
          end: 550,
          id: 7,
          temperature: 48,
        },
        {
          start: 550,
          end: 600,
          id: 8,
          temperature: 28,
        },
        {
          start: 600,
          end: 650,
          id: 9,
          temperature: 58,
        },
        {
          start: 650,
          end: 700,
          id: 10,
          temperature: 38,
        },
        {
          start: 700,
          end: 750,
          id: 11,
          temperature: 48,
        },
        {
          start: 750,
          end: 800,
          id: 12,
          temperature: 28,
        },
        {
          start: 800,
          end: 850,
          id: 13,
          temperature: 58,
        },
        {
          start: 850,
          end: 900,
          id: 14,
          temperature: 38,
        },
        {
          start: 900,
          end: 950,
          id: 15,
          temperature: 48,
        },
        {
          start: 950,
          end: 1000,
          id: 16,
          temperature: 28,
        },
        {
          start: 1000,
          end: 1050,
          id: 17,
          temperature: 58,
        },
        {
          start: 1050,
          end: 1100,
          id: 18,
          temperature: 38,
        },
        {
          start: 1100,
          end: 1150,
          id: 19,
          temperature: 48,
        },
        {
          start: 1150,
          end: 1200,
          id: 20,
          temperature: 28,
        },
        {
          start: 1200,
          end: 1250,
          id: 21,
          temperature: 58,
        },
        {
          start: 1250,
          end: 1300,
          id: 22,
          temperature: 38,
        },
      ]

      const temp = [-1, 1]
      for (const [i, item] of list.entries()) {
        if (item.start >= totalLength) continue
        item.temperature += temp[index % 2] * Math.random() * 10

        /**
         * 假0~500m设有一条管道
         * */
        const startDistance = item.start
        const endDistance = Math.min(item.end, totalLength)
        // 每隔多少米一个点
        const segmentLength = 2
        const segmentT = segmentLength / totalLength
        const pointCount = Math.max(Math.floor((endDistance - startDistance) / segmentLength) - 1, 0)
        const startT = startDistance / totalLength
        const segmentPoints = [curve.getPoint(startT)]
        for (let i = 1; i <= pointCount; i++) {
          const t = startT + i * segmentT
          segmentPoints.push(curve.getPoint(t))
        }

        segmentPoints.push(curve.getPoint(endDistance / totalLength))
        const segmentCurve = new THREE.CatmullRomCurve3(segmentPoints)
        const segmentTubeGeometry = new THREE.TubeGeometry(segmentCurve, Math.floor(endDistance - startDistance), 0.2, 12, false)

        const canvas = document.createElement('canvas')
        canvas.width = 256
        canvas.height = 256
        const ctx = canvas.getContext('2d')

        // 创建沿管道长度方向的线性渐变
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)
        const startColor = i > 0 ? getTemperatureColor(list[i - 1].temperature) : getTemperatureColor(0)
        gradient.addColorStop(0, startColor)
        gradient.addColorStop(1, getTemperatureColor(item.temperature))

        ctx.fillStyle = gradient
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        const texture = new THREE.CanvasTexture(canvas)
        texture.colorSpace = THREE.SRGBColorSpace

        // 创建管道材质，使用加载的纹理
        // const segmentTubeMaterial = new THREE.MeshStandardMaterial({
        //   map: texture,
        //   side: THREE.DoubleSide,
        //   roughness: 1,
        //   metalness: 0,
        // })

        const segmentTubeMaterial = new THREE.MeshPhysicalMaterial({
          map: texture,
          transmission: 0.5, // 设置透射度，1.0表示完全透明
          ior: 1.5, // 设置折射率，玻璃的典型值
          roughness: 0.8, // 设置粗糙度，0.0表示完全光滑
          metalness: 0.5, // 设置金属度，0.0表示非金属
          color: 0xffffff, // 设置颜色，可以根据需要调整
          side: THREE.DoubleSide,
          // opacity: 0.5, // 设置透明度，如果需要半透明效果
          // transparent: true // 启用透明度
        })

        segmentTubeMaterial.onBeforeCompile = function(shader) {
          console.log(shader.fragmentShader, 'shader==================')

          const uniforms = {
            rimColor: { value: new THREE.Color(0xff00ff) },
            rimPower: { value: 0.6 },
          }
          shader.uniforms.rimColor = uniforms.rimColor
          shader.uniforms.rimPower = uniforms.rimPower

          shader.fragmentShader = `
                    uniform vec3 rimColor;
                    uniform float rimPower;
                        ${shader.fragmentShader.replace(
    '#include <dithering_fragment>',
    '#include <dithering_fragment>\n\tfloat dotNV = 1.0-saturate( dot( normal, geometry.viewDir ) );\n\tgl_FragColor.rgb += rimPower*dotNV*rimColor;'
  )}
                `
        }

        const segmentTube = new THREE.Mesh(segmentTubeGeometry, segmentTubeMaterial)
        segmentTube.name = item.name
        // tube.visible = false
        scene.add(segmentTube)
      }
    }
  }

  // 存储光源引用以便清理
  lights = []

  /** 添加光源 - 优化版本，减少光源数量提升性能*/
  #addLight() {
    // 环境光 - 提供基础照明
    const ambientLight = new THREE.AmbientLight(0xffffff, 5)
    scene.add(ambientLight)
    this.lights.push(ambientLight)
  }

  dispose() {
    // 清理光源
    this.lights = []

    // 清理加载器
    if (this.rgbeLoader) {
      this.rgbeLoader = null
    }

    // 清理加载管理器
    this.loadingManager = null
  }
}
